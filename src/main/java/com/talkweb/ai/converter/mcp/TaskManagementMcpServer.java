package com.talkweb.ai.converter.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.converter.web.entity.ConversionTask;
import com.talkweb.ai.converter.web.service.ConversionTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.mcp.server.McpFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * MCP Server for Task Management
 *
 * Provides CRUD operations for conversion tasks through MCP WebMVC functions.
 * This allows external AI agents to manage document conversion tasks.
 */
@Component
@Profile("server")
@ConditionalOnProperty(name = "spring.ai.mcp.server.enabled", havingValue = "true", matchIfMissing = false)
public class TaskManagementMcpServer {

    private static final Logger logger = LoggerFactory.getLogger(TaskManagementMcpServer.class);

    @Autowired
    private ConversionTaskService taskService;

    @Autowired
    private ObjectMapper objectMapper;

    public TaskManagementMcpServer() {
        logger.info("Initializing Task Management MCP Server with WebMVC functions");
    }

    /**
     * List conversion tasks with pagination
     */
    @McpFunction(name = "list_tasks", description = "List conversion tasks with pagination")
    public Map<String, Object> listTasks(
            @McpParameter(name = "page", description = "Page number (0-based)", defaultValue = "0") int page,
            @McpParameter(name = "size", description = "Page size", defaultValue = "10") int size,
            @McpParameter(name = "status", description = "Task status filter", required = false) String status) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ConversionTask> taskPage;

            if (status != null && !status.isEmpty()) {
                ConversionTask.TaskStatus taskStatus = ConversionTask.TaskStatus.valueOf(status.toUpperCase());
                taskPage = taskService.findByStatus(taskStatus, pageable);
            } else {
                taskPage = taskService.findAll(pageable);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("tasks", taskPage.getContent());
            result.put("totalElements", taskPage.getTotalElements());
            result.put("totalPages", taskPage.getTotalPages());
            result.put("currentPage", taskPage.getNumber());
            result.put("size", taskPage.getSize());

            logger.debug("Listed {} tasks (page {}/{})", taskPage.getNumberOfElements(),
                        page + 1, taskPage.getTotalPages());

            return result;
        } catch (Exception e) {
            logger.error("Error listing tasks", e);
            return Map.of("error", "Failed to list tasks: " + e.getMessage());
        }
    }

    /**
     * Get a specific conversion task by ID
     */
    private Map<String, Object> getTask(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            Optional<ConversionTask> task = taskService.findById(taskId);
            if (task.isPresent()) {
                logger.debug("Retrieved task: {}", taskId);
                return Map.of("task", task.get());
            } else {
                return Map.of("error", "Task not found: " + taskId);
            }
        } catch (Exception e) {
            logger.error("Error getting task", e);
            return Map.of("error", "Failed to get task: " + e.getMessage());
        }
    }

    /**
     * Create a new conversion task
     */
    private Map<String, Object> createTask(Map<String, Object> params) {
        try {
            String fileName = (String) params.get("fileName");
            String sourceFormat = (String) params.get("sourceFormat");
            String targetFormat = (String) params.get("targetFormat");

            if (fileName == null || sourceFormat == null || targetFormat == null) {
                return Map.of("error", "fileName, sourceFormat, and targetFormat are required");
            }

            ConversionTask task = new ConversionTask();
            task.setFileName(fileName);
            task.setSourceFormat(sourceFormat);
            task.setTargetFormat(targetFormat);
            task.setStatus(ConversionTask.TaskStatus.PENDING);
            task.setCreatedAt(LocalDateTime.now());

            // Set optional parameters
            if (params.containsKey("priority")) {
                task.setPriority((Integer) params.get("priority"));
            }
            if (params.containsKey("options")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> options = (Map<String, Object>) params.get("options");
                task.setOptions(objectMapper.writeValueAsString(options));
            }

            ConversionTask savedTask = taskService.save(task);
            logger.info("Created new task: {} for file: {}", savedTask.getId(), fileName);

            return Map.of("task", savedTask, "message", "Task created successfully");
        } catch (Exception e) {
            logger.error("Error creating task", e);
            return Map.of("error", "Failed to create task: " + e.getMessage());
        }
    }

    /**
     * Update an existing conversion task
     */
    private Map<String, Object> updateTask(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            Optional<ConversionTask> optionalTask = taskService.findById(taskId);
            if (!optionalTask.isPresent()) {
                return Map.of("error", "Task not found: " + taskId);
            }

            ConversionTask task = optionalTask.get();

            // Update allowed fields
            if (params.containsKey("priority")) {
                task.setPriority((Integer) params.get("priority"));
            }
            if (params.containsKey("options")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> options = (Map<String, Object>) params.get("options");
                task.setOptions(objectMapper.writeValueAsString(options));
            }

            task.setUpdatedAt(LocalDateTime.now());
            ConversionTask updatedTask = taskService.save(task);

            logger.info("Updated task: {}", taskId);
            return Map.of("task", updatedTask, "message", "Task updated successfully");
        } catch (Exception e) {
            logger.error("Error updating task", e);
            return Map.of("error", "Failed to update task: " + e.getMessage());
        }
    }

    /**
     * Delete a conversion task
     */
    private Map<String, Object> deleteTask(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            if (!taskService.existsById(taskId)) {
                return Map.of("error", "Task not found: " + taskId);
            }

            taskService.deleteById(taskId);
            logger.info("Deleted task: {}", taskId);

            return Map.of("message", "Task deleted successfully", "taskId", taskId);
        } catch (Exception e) {
            logger.error("Error deleting task", e);
            return Map.of("error", "Failed to delete task: " + e.getMessage());
        }
    }

    /**
     * Get the status of a conversion task
     */
    private Map<String, Object> getTaskStatus(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            Optional<ConversionTask> task = taskService.findById(taskId);
            if (task.isPresent()) {
                ConversionTask t = task.get();
                Map<String, Object> status = new HashMap<>();
                status.put("taskId", t.getId());
                status.put("status", t.getStatus());
                status.put("progress", t.getProgress());
                status.put("fileName", t.getFileName());
                status.put("createdAt", t.getCreatedAt());
                status.put("updatedAt", t.getUpdatedAt());
                if (t.getCompletedAt() != null) {
                    status.put("completedAt", t.getCompletedAt());
                }
                if (t.getErrorMessage() != null) {
                    status.put("errorMessage", t.getErrorMessage());
                }

                return status;
            } else {
                return Map.of("error", "Task not found: " + taskId);
            }
        } catch (Exception e) {
            logger.error("Error getting task status", e);
            return Map.of("error", "Failed to get task status: " + e.getMessage());
        }
    }

    /**
     * Cancel a running conversion task
     */
    private Map<String, Object> cancelTask(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            boolean cancelled = taskService.cancelTask(taskId);
            if (cancelled) {
                logger.info("Cancelled task: {}", taskId);
                return Map.of("message", "Task cancelled successfully", "taskId", taskId);
            } else {
                return Map.of("error", "Failed to cancel task or task not found: " + taskId);
            }
        } catch (Exception e) {
            logger.error("Error cancelling task", e);
            return Map.of("error", "Failed to cancel task: " + e.getMessage());
        }
    }

    /**
     * Retry a failed conversion task
     */
    private Map<String, Object> retryTask(Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return Map.of("error", "Task ID is required");
            }

            Optional<ConversionTask> optionalTask = taskService.findById(taskId);
            if (!optionalTask.isPresent()) {
                return Map.of("error", "Task not found: " + taskId);
            }

            ConversionTask task = optionalTask.get();
            if (task.getStatus() != ConversionTask.TaskStatus.FAILED) {
                return Map.of("error", "Only failed tasks can be retried");
            }

            // Reset task for retry
            task.setStatus(ConversionTask.TaskStatus.PENDING);
            task.setProgress(0);
            task.setErrorMessage(null);
            task.setUpdatedAt(LocalDateTime.now());

            ConversionTask retriedTask = taskService.save(task);
            logger.info("Retrying task: {}", taskId);

            return Map.of("task", retriedTask, "message", "Task queued for retry");
        } catch (Exception e) {
            logger.error("Error retrying task", e);
            return Map.of("error", "Failed to retry task: " + e.getMessage());
        }
    }
}
