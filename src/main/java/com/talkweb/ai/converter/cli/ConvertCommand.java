package com.talkweb.ai.converter.cli;

import com.talkweb.ai.converter.cli.util.ConsoleColors;
import com.talkweb.ai.converter.cli.util.ConsoleLogger;
import com.talkweb.ai.converter.cli.util.ProgressBar;
import com.talkweb.ai.converter.core.*;
import com.talkweb.ai.converter.core.impl.DocumentProcessingChain;
import com.talkweb.ai.converter.service.FileScannerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.ParentCommand;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Command(
    name = "convert",
    description = "Convert documents to Markdown format",
    mixinStandardHelpOptions = true
)
public class ConvertCommand implements Callable<Integer> {

    @ParentCommand
    private DocConverterCommand parent;

    // 为测试提供的方法
    void setParentCommand(DocConverterCommand parent) {
        this.parent = parent;
    }

    private final PluginManager pluginManager;
    private final FileScannerService fileScannerService;

    public ConvertCommand(PluginManager pluginManager, FileScannerService fileScannerService) {
        this.pluginManager = pluginManager;
        this.fileScannerService = fileScannerService;
    }

    @Option(
        names = {"-i", "--input"},
        description = "Input file or directory",
        required = true
    )
    private Path input;

    @Option(
        names = {"-o", "--output"},
        description = "Output directory (default: ${DEFAULT-VALUE})",
        defaultValue = "output" , required = false
    )
    private Path outputDir;

    @Option(
        names = {"-r", "--recursive"},
        description = "Process directories recursively",
        required = false,
        defaultValue = "true"
    )
    private boolean recursive;

    @Option(
        names = {"--include"},
        description = "File patterns to include (comma-separated, e.g., '*.pdf,*.docx')",
        split = ",",
        defaultValue="*.pptx,*.docx,*.pdf,*.html,*.htm,*.txt,*.xls,*.xlsx,*.odt,*.jpeg,*.jpg,*.png", // 默认包含常见文档格式
        required=false
    )
    private String[] includePatterns;

    @Option(
        names = {"--exclude"},
        description = "File patterns to exclude (comma-separated)",
        split = ",",
        defaultValue = ".*\\.tmp,.*\\.bak,.*~,.js,.css,.log" // 默认排除临时和备份文件
    )
    private String[] excludePatterns;

    @Option(
        names = {"--force"},
        description = "Force conversion even if output file exists" ,
        required = false
    )
    private boolean force;

    @Option(
        names = {"--parallel"},
        description = "Enable parallel processing",
        required = false
    )
    private boolean parallel;

    @Option(
        names = {"--threads"},
        description = "Number of threads for parallel processing (default: number of CPU cores)" ,
        required = false
    )
    private int threads = Runtime.getRuntime().availableProcessors();

    @Option(
        names = {"--no-color"},
        description = "Disable colored output",
         required = false
    )
    private boolean noColor;

    @Option(
        names = {"--ai"},
        description = "Enable AI content enhancement" ,
         required = false         
    )
    private boolean enableAI;

    @Option(
        names = {"--ai-model"},
        description = "Specify AI model to use (default: ${DEFAULT-VALUE})",
        defaultValue = "qwen3-14b",
         required = false
    )
    private String aiModel;

    @Option(
        names = {"--report"},
        description = "Generate conversion report in JSON format",
         required = false,
         defaultValue = "true"
    )
    private boolean generateReport;

    @Option(
        names = {"--report-file"},
        description = "Report file path (default: ${DEFAULT-VALUE})",
        defaultValue = "conversion_report.json",
          required = false
    )
    private Path reportFile;

    @Option(
        names = {"--log-level"},
        description = "Set log level (DEBUG, INFO, WARNING, ERROR)",
          required = false,
defaultValue = "INFO"
    )
    private ConsoleLogger.Level logLevel = ConsoleLogger.Level.INFO;

    @Option(
        names = {"--html-mode"},
        description = "HTML conversion mode (STRICT or LOOSE, default: ${DEFAULT-VALUE})",
        defaultValue = "LOOSE",
          required = false

    )
    private com.talkweb.ai.converter.util.HtmlConversionMode htmlMode;

    private ConsoleLogger logger = new ConsoleLogger();

    @Override
    public Integer call() throws Exception {
        // 配置日志和颜色
        if (noColor) {
            ConsoleColors.disable();
        }

        logger.setLevel(logLevel);
        logger.setShowTimestamp(parent.isVerbose());

        // 初始化AI配置
        if (enableAI) {
            logger.info("AI enhancement enabled using model: %s", aiModel);
            System.setProperty("spring.ai.model", aiModel);
        }

        // 初始化文档处理器
        DocumentProcessor processor = createDocumentProcessorChain();
        if (processor == null) {
            logger.error("No document processor plugins found. Please install at least one document processor plugin.");
            return 1;
        }

        // 初始化报告收集器
        ConversionReport report = new ConversionReport();
        report.setStartTime(System.currentTimeMillis());
        report.setAiEnabled(enableAI);
        report.setAiModel(aiModel);

        // 创建输出目录
        try {
            Files.createDirectories(outputDir);
            logger.debug("Created output directory: %s", outputDir);
        } catch (IOException e) {
            logger.error("Failed to create output directory: %s", e.getMessage());
            return 1;
        }

        // 处理输入文件/目录
        if (Files.isDirectory(input)) {
            logger.info("Scanning directory: %s", input);
            List<Path> filesToProcess = fileScannerService.scan(input, recursive, includePatterns, excludePatterns);

            if (filesToProcess.isEmpty()) {
                logger.warning("No files found matching the specified criteria.");
                return 0;
            }

            logger.info("Found %d files to process", filesToProcess.size());

            if (parallel && filesToProcess.size() > 1) {
                processFilesInParallel(filesToProcess, processor);
            } else {
                processFilesSequentially(filesToProcess, processor);
            }
        } else {
            if (fileScannerService.shouldProcessFile(input, includePatterns, excludePatterns)) {
                logger.info("Processing file: %s", input);
                processFile(input, processor);
            } else {
                logger.warning("File does not match the specified criteria: %s", input);
            }
        }

        report.setEndTime(System.currentTimeMillis());
        logger.success("Conversion completed successfully!");

        // 生成报告
        if (generateReport) {
            try {
                String reportJson = new ObjectMapper().writeValueAsString(report);
                Files.writeString(reportFile, reportJson);
                logger.info("Conversion report saved to: %s", reportFile);
            } catch (IOException e) {
                logger.error("Failed to generate conversion report: %s", e.getMessage());
            }
        }

        return 0;
    }

    private static class ConversionReport {
        private long startTime;
        private long endTime;
        private boolean aiEnabled;
        private String aiModel;
        private int totalFiles;
        private int successCount;
        private int failCount;
        // Getters and setters
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public void setAiEnabled(boolean aiEnabled) { this.aiEnabled = aiEnabled; }
        public void setAiModel(String aiModel) { this.aiModel = aiModel; }
        // Additional methods to update counts
    }

    /**
     * 顺序处理文件
     * @param files 文件列表
     * @param processor 文档处理器
     */
    private void processFilesSequentially(List<Path> files, DocumentProcessor processor) {
        ProgressBar progressBar = new ProgressBar("Converting", files.size());

        int successCount = 0;
        int failCount = 0;

        for (Path file : files) {
            boolean success = processFile(file, processor);
            if (success) {
                successCount++;
            } else {
                failCount++;
            }
            progressBar.step();
        }

        logger.info("Processed %d files: %d succeeded, %d failed",
            files.size(), successCount, failCount);
    }

    /**
     * 并行处理文件
     * @param files 文件列表
     * @param processor 文档处理器
     */
    private void processFilesInParallel(List<Path> files, DocumentProcessor processor) {
        logger.info("Processing files in parallel with %d threads", threads);

        ExecutorService executor = Executors.newFixedThreadPool(threads);
        ProgressBar progressBar = new ProgressBar("Converting", files.size());

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        for (Path file : files) {
            executor.submit(() -> {
                boolean success = processFile(file, processor);
                if (success) {
                    successCount.incrementAndGet();
                } else {
                    failCount.incrementAndGet();
                }
                progressBar.step();
            });
        }

        executor.shutdown();
        try {
            executor.awaitTermination(1, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            logger.error("Processing interrupted", e);
            Thread.currentThread().interrupt();
        }

        logger.info("Processed %d files: %d succeeded, %d failed",
            files.size(), successCount.get(), failCount.get());
    }

    private DocumentProcessor createDocumentProcessorChain() {
        List<DocumentProcessor> processors = pluginManager.getPlugins().stream()
                .filter(p -> p instanceof DocumentProcessor)
                .map(p -> (DocumentProcessor) p)
                .collect(Collectors.toList());

        if (processors.isEmpty()) {
            return null;
        }

        return new DocumentProcessingChain(processors);
    }

    /**
     * 处理单个文件
     * @param file 文件路径
     * @param processor 文档处理器
     * @return 处理是否成功
     */
    private boolean processFile(Path file, DocumentProcessor processor) {
        try {
            String ext = FilenameUtils.getExtension(file.toString());

            // 检查文件是否支持
            if (!processor.supports(ext)) {
                if (parent.isVerbose()) {
                    logger.debug("Skipping unsupported file: %s (extension: %s)", file, ext);
                }
                return false;
            }

            // 构建处理上下文
            Path relativePath = input.toAbsolutePath().relativize(file.toAbsolutePath());
            Path outputFile = outputDir.resolve(relativePath)
                .getParent()
                .resolve(FilenameUtils.getBaseName(file.toString()) + ".md");

            // 检查输出文件是否存在
            if (Files.exists(outputFile) && !force) {
                logger.warning("Output file already exists, skipping: %s", outputFile);
                return false;
            }

            // 确保输出目录存在
            Files.createDirectories(outputFile.getParent());

            // 处理文件
            ProcessingContext context = new ProcessingContext.Builder()
                .setOutputPath(outputFile.getParent())
                .setForce(force)
                .setHtmlConversionMode(htmlMode)
                .build();

            if (parent.isVerbose()) {
                logger.debug("Processing file: %s -> %s", file, outputFile);
            }

            // 模拟处理耗时
            try {
                Thread.sleep(500); // 模拟500ms处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            ProcessingResult result = processor.process(file.toFile(), context);

            if (result.isSuccess()) {
                if (parent.isVerbose()) {
                    logger.success("Processed: %s -> %s", file, result.getOutputFile());
                }
                return true;
            } else {
                logger.error("Failed to process: %s", file);
                if (result.getError() != null) {
                    logger.error("Reason: %s", result.getError().getMessage());
                }
                return false;
            }
        } catch (Exception e) {
            logger.error("Error processing file: %s", file);
            if (parent.isVerbose()) {
                logger.error(e.getMessage(), e);
            } else {
                logger.error(e.getMessage());
            }
            return false;
        }
    }


}
